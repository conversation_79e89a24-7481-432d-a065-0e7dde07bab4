import React from 'react';
import { DynamicViewportExample } from './index';

/**
 * 测试组件：展示移动端适配和 transType props 功能
 */
export function TestMobileDynamicViewportExample() {
  return (
    <div className="space-y-8 p-4">
      <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="font-semibold text-yellow-800 mb-2">移动端适配测试说明</h3>
        <p className="text-yellow-700 text-sm mb-2">
          移动端检测已改进，现在综合判断以下条件：
        </p>
        <ul className="text-yellow-700 text-sm list-disc list-inside space-y-1">
          <li>屏幕宽度小于 768px 且支持触摸</li>
          <li>或者检测到移动端用户代理</li>
          <li>移动端下按钮将被隐藏，所有变换功能将被禁用</li>
          <li>组件将使用固定尺寸，确保在移动端正常显示</li>
        </ul>
      </div>

      <div>
        <h2 className="text-lg font-semibold mb-4">旋转模式 (rotation)</h2>
        <DynamicViewportExample 
          unit="dvw" 
          transType="rotation"
          colorStyles="dark:bg-green-500 bg-green-500 border border-green-400"
        />
      </div>
      
      <div>
        <h2 className="text-lg font-semibold mb-4">交换模式 (swap) - 带动画</h2>
        <DynamicViewportExample 
          unit="dvw" 
          transType="swap"
          colorStyles="dark:bg-purple-500 bg-purple-500 border border-purple-400"
        />
      </div>
      
      <div>
        <h2 className="text-lg font-semibold mb-4">默认模式 (swap)</h2>
        <DynamicViewportExample 
          unit="lvw"
          colorStyles="dark:bg-orange-500 bg-orange-500 border border-orange-400"
        />
      </div>

      <div>
        <h2 className="text-lg font-semibold mb-4">高度单位测试 (无按钮)</h2>
        <DynamicViewportExample 
          unit="dvh"
          transType="rotation"
          colorStyles="dark:bg-blue-500 bg-blue-500 border border-blue-400"
        />
      </div>
    </div>
  );
}
