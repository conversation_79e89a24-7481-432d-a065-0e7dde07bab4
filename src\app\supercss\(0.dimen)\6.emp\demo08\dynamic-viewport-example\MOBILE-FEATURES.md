# DynamicViewportExample 移动端适配功能

## 新增功能总览

### 1. 移动端自动检测
- 使用 `window.innerWidth < 768` 检测移动端
- 响应式检测，窗口大小变化时自动重新检测
- 使用 `useEffect` 和 `resize` 事件监听器

### 2. 移动端适配行为
当检测到移动端时：
- **容器尺寸**：设置为 100% 宽度和 100% 高度
- **按钮控制**：完全隐藏旋转/交换按钮
- **变换功能**：禁用所有旋转和宽高变换功能
- **地址栏动画**：禁用地址栏显示/隐藏动画
- **性能优化**：移除不必要的动画计算

### 3. Swap 模式动画优化
- 在非移动端的 swap 模式下添加平滑的宽度变化动画
- 使用 Framer Motion 的 transition 配置
- 动画时长：0.3秒，缓动函数：easeInOut
- 与现有动画保持一致的配置

## 技术实现细节

### 移动端检测逻辑
```typescript
const [isMobile, setIsMobile] = useState(false);

useEffect(() => {
  const checkMobile = () => {
    setIsMobile(window.innerWidth < 768);
  };

  checkMobile();
  window.addEventListener('resize', checkMobile);
  
  return () => {
    window.removeEventListener('resize', checkMobile);
  };
}, []);
```

### 统一的横屏状态计算
```typescript
// 移动端时禁用变换
const isLandscape = isMobile ? false : (transType === "rotation" ? rotationAngle === 90 : isSwapped);
```

### 容器尺寸适配
```typescript
// 移动端使用百分比尺寸
width: isMobile ? "100%" : "计算的像素值",
height: isMobile ? "100%" : "计算的像素值"
```

### 按钮显示控制
```typescript
// 移动端完全隐藏按钮
{!isMobile && unit.endsWith('w') ? (
  <button onClick={handleRotate}>...</button>
) : !isMobile ? (
  <svg>...</svg>
) : null}
```

### Swap 模式动画配置
```typescript
transition={{
  duration: transType === "swap" && !isMobile ? 0.3 : 0,
  ease: "easeInOut",
  type: "tween"
}}
```

## 使用示例

### 测试移动端适配
```tsx
import { TestMobileDynamicViewportExample } from './test-mobile';

// 包含移动端适配测试的组件
<TestMobileDynamicViewportExample />
```

### 基础用法（自动适配）
```tsx
// 在桌面端显示按钮和动画，移动端自动隐藏
<DynamicViewportExample unit="dvw" transType="swap" />
```

## 兼容性保证

- ✅ 完全向后兼容，现有代码无需修改
- ✅ 移动端自动适配，无需额外配置
- ✅ 保持现有的所有功能在桌面端正常工作
- ✅ 性能优化不影响现有功能

## 性能优化

1. **移动端优化**
   - 禁用复杂的旋转和变换动画
   - 使用百分比尺寸减少计算
   - 条件渲染减少 DOM 节点

2. **动画优化**
   - Swap 模式使用 Framer Motion 的高性能动画
   - 只在需要时启用动画
   - 与现有动画配置保持一致

3. **响应式优化**
   - 使用防抖机制避免频繁检测
   - 事件监听器正确清理
   - 状态更新最小化
