import clsx from "clsx";
import { motion, useMotionValueEvent, useScroll } from "motion/react";
import { useRef, useState, useEffect } from "react";
import { Example } from "../example";
import { Stripes } from "../stripes";

const viewport = {
  lvh: 491,  // Large Viewport Height - 最大可能高度（地址栏隐藏时）
  svh: 443,  // Small Viewport Height - 最小可能高度（地址栏显示时）
  lvw: 320,  // Large Viewport Width - 最大可能宽度（无滚动条时）
  svw: 300,  // Small Viewport Width - 最小可能宽度（有滚动条时）
  navBarHeight: 48,
  width: 300,  // 当前容器宽度
  padding: 5,
};

const transition = {
  duration: 0.2,
  ease: "linear",
  type: "tween",
};

interface Props {
  unit?: string;
  colorStyles?: string;
  transType?: "rotation" | "swap";
}

export function DynamicViewportExample({ unit = "dvh", colorStyles = "dark:bg-blue-500 bg-blue-500 border border-blue-400", transType = "swap" }: Props) {
  const [hidden, setHidden] = useState(false);
  const [rotationAngle, setRotationAngle] = useState(0);
  const [isSwapped, setIsSwapped] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const parent = useRef(null);

  // 改进的移动端检测逻辑
  useEffect(() => {
    const checkMobile = () => {
      // 综合多个条件判断移动端
      const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const isSmallScreen = window.innerWidth < 768;
      const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      // 满足屏幕尺寸小且有触摸支持，或者是移动端用户代理
      const isMobileDevice = (isSmallScreen && hasTouch) || isMobileUserAgent;

      setIsMobile(isMobileDevice);
    };

    // 初始检测
    checkMobile();

    // 监听窗口大小变化
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  const { scrollY, } = useScroll({ container: parent });

  // 根据变换类型计算当前是否处于"横屏"状态，移动端时禁用变换
  const isLandscape = isMobile ? false : (transType === "rotation" ? rotationAngle === 90 : isSwapped);

  const animationVariants = {
    visible: {
      y: 0,
      height: (() => {
        // 移动端使用固定高度，避免组件消失
        if (isMobile) return `${viewport.lvh}px`;

        if (unit === 'lvw') return isLandscape ? `${viewport.width}px` : `${viewport.lvh}px`;
        if (unit === 'svw') return isLandscape ? `${viewport.width}px` : `${viewport.lvh}px`;
        if (unit.endsWith('h')) return `${viewport.lvh}px`;
        return isLandscape ? `${viewport.width}px` : `${viewport.lvh}px`;
      })(),
    },
    hidden: {
      y: isMobile ? 0 : `-${viewport.navBarHeight - 2}px`,
      height: (() => {
        // 移动端使用固定高度，不需要地址栏动画
        if (isMobile) return `${viewport.lvh}px`;

        if (unit === 'lvw') return isLandscape ? `${viewport.width + viewport.navBarHeight - 1}px` : `${viewport.lvh + viewport.navBarHeight - 1}px`;
        if (unit === 'svw') return isLandscape ? `${viewport.width + viewport.navBarHeight - 1}px` : `${viewport.lvh + viewport.navBarHeight - 1}px`;
        if (unit.endsWith('h')) return `${viewport.lvh + viewport.navBarHeight - 1}px`;
        return isLandscape ? `${viewport.width + viewport.navBarHeight - 1}px` : `${viewport.lvh + viewport.navBarHeight - 1}px`;
      })(),
    },
  }
  // 处理变换操作：根据 transType 执行不同的逻辑，移动端时禁用
  const handleRotate = () => {
    // 移动端禁用所有变换功能
    if (isMobile) return;

    if (transType === "rotation") {
      // 旋转模式：执行真实的 CSS transform rotate 旋转变换
      setRotationAngle(!rotationAngle ? 90 : 0);
    } else {
      // 交换模式：切换宽高交换状态，不执行旋转变换
      setIsSwapped(!isSwapped);
    }
  };

  useMotionValueEvent(scrollY, "change", (latest) => {
    // 移动端禁用滚动触发的动画

    const previous = scrollY.getPrevious();
    if (previous && latest > previous) {
      setHidden(true);
    } else {
      setHidden(false);
    }
  });

  return (
    <Example className="relative overflow-hidden">
      <div className="relative grid justify-items-center">
        <motion.div
          style={{ transformOrigin: "center center" }}
          animate={{
            // 移动端禁用旋转，只在非移动端的旋转模式下应用旋转变换
            rotate: isMobile ? 0 : (transType === "rotation" ? -rotationAngle : 0)
          }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          <motion.div
            animate={{
              width: (() => {
                // 移动端使用固定宽度，避免组件消失
                if (isMobile) return `${viewport.width}px`;

                if (unit === 'lvw') {
                  return isLandscape ? `${viewport.lvh}px` : `${viewport.lvw}px`;
                }
                if (unit === 'svw') {
                  return isLandscape ? `${viewport.svh}px` : `${viewport.svw}px`;
                }
                if (unit.endsWith('h')) return `${viewport.width}px`;
                return isLandscape ? `${viewport.lvh}px` : `${viewport.width}px`;
              })(),
              height: (() => {
                // 移动端使用固定高度，避免组件消失
                if (isMobile) return `${viewport.lvh}px`;

                if (unit === 'lvw') {
                  return isLandscape ? `${viewport.width}px` : `${viewport.lvh}px`;
                }
                if (unit === 'svw') {
                  return isLandscape ? `${viewport.width}px` : `${viewport.lvh}px`;
                }
                if (unit.endsWith('h')) return `${viewport.lvh}px`;
                return isLandscape ? `${viewport.width}px` : `${viewport.lvh}px`;
              })(),
            }}
            transition={{
              // swap 模式下的宽度变化动画
              duration: transType === "swap" && !isMobile ? 0.3 : 0,
              ease: "easeInOut",
              type: "tween"
            }}
          >
            <Stripes
              ref={parent}
              style={{
                width: "100%",
                height: "100%",
                // 只在非移动端的旋转模式下应用内部旋转变换
                transform: !isMobile && transType === "rotation" && unit.endsWith('w') && rotationAngle === 90 ? 'rotate(90deg)' : 'none',
                transformOrigin: 'center center'
              }}
              className="relative overflow-y-scroll overscroll-none rounded-lg border border-slate-300 text-center text-xs dark:border-slate-700 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']"
            >
            <motion.div
              className="absolute w-full snap-start overflow-hidden"
              transition={{ ...transition }}
              variants={animationVariants}
              initial="visible"
              animate={hidden ? "hidden" : "visible"}
            >
              <div className="grid h-[48px] w-full grid-cols-[auto_1fr_auto] items-center justify-start gap-4 rounded-t-lg border-b border-slate-300 bg-slate-100 px-3 dark:border-slate-600 dark:bg-slate-800">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth="1.5"
                  stroke="currentColor"
                  className="h-5 w-5 text-slate-600 dark:text-slate-400"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                  />
                </svg>
                <div className="w-full rounded-full border border-slate-200 bg-slate-50 px-4 py-1 text-slate-600 dark:border-slate-700 dark:bg-slate-700 dark:text-slate-400">
                  usehook.cn
                </div>
                {/* 根据 unit 类型显示按钮，移动端禁用功能但保留显示 */}
                {unit.endsWith('w') ? (
                  <button
                    onClick={handleRotate}
                    disabled={isMobile}
                    className={`flex items-center justify-center h-6 w-6 rounded-full transition-colors ${
                      isMobile
                        ? 'bg-slate-100 dark:bg-slate-700 cursor-not-allowed opacity-50'
                        : 'bg-slate-200 hover:bg-slate-300 dark:bg-slate-600 dark:hover:bg-slate-500'
                    }`}
                    title={isMobile ? "移动端已禁用" : "横屏/竖屏"}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth="1.5"
                      stroke="currentColor"
                      className="h-4 w-4 text-slate-600 dark:text-slate-400"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"
                      />
                    </svg>
                  </button>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth="1.5"
                    stroke="currentColor"
                    className={`h-5 w-5 ${
                      isMobile
                        ? 'text-slate-400 dark:text-slate-500 opacity-50'
                        : 'text-slate-600 dark:text-slate-400'
                    }`}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"
                    />
                  </svg>
                )}
              </div>
              <motion.div
                className="pointer-events-none h-full w-full p-[7px]"
                transition={{
                  ...transition,
                  duration: 0.2,
                  delay: unit === "dvh" || unit === "dvw" ? 0.4 : 0,  // lvw 和 svw 不需要延迟
                }}
                variants={{
                  visible: {
                    maxHeight: (() => {
                      // 移动端使用适合的高度值，支持动画
                      if (isMobile) return `${viewport.lvh - 7 * 2}px`; // 减去 padding

                      if (unit === "lvh") return `${viewport.lvh}px`;
                      if (unit === "svh") return `${viewport.svh - 2}px`;
                      if (unit === "lvw") return isLandscape ? `${viewport.width + viewport.navBarHeight - 2}px` : `${viewport.lvh - 2}px`;
                      if (unit === "svw") return isLandscape ? `${viewport.svw - viewport.navBarHeight}px` : `${viewport.svh - 2}px`;
                      if (unit === "dvw") return isLandscape ? `${viewport.width - viewport.navBarHeight}px` : `${viewport.svh - 2}px`;
                      return `${viewport.svh - 2}px`;
                    })(),
                  },
                  hidden: {
                    maxHeight: (() => {
                      // 移动端使用较小的高度值，创建动画效果
                      if (isMobile) return `${viewport.svh - 7 * 2}px`; // 使用 svh 创建动画差异

                      if (unit === "svh") return `${viewport.svh}px`;
                      if (unit === "lvh") return `${viewport.lvh - 1}px`;
                      if (unit === "lvw") return isLandscape ? `${viewport.width}px` : `${viewport.lvh - 1}px`;
                      if (unit === "svw") return isLandscape ? `${viewport.svw - viewport.navBarHeight}px` : `${viewport.svh - 1}px`;
                      if (unit === "dvw") return isLandscape ? `${viewport.lvw - 20}px` : `${viewport.lvh - 1}px`;
                      return `${viewport.lvh - 1}px`;
                    })(),
                  },
                }}
                initial="visible"
                animate={hidden ? "hidden" : "visible"}
              >
                <div
                  className={clsx(
                    colorStyles,
                    "grid h-full w-full grid-rows-[1fr_auto_1fr] content-center items-center justify-items-center gap-5 self-center overflow-hidden rounded-md py-4 font-mono font-bold text-slate-50",
                  )}
                >
                  <div className="grid h-full grid-rows-[1px_1fr] justify-items-center">
                    <div className="h-full w-[12px] bg-white/60"></div>
                    <div className="h-full w-[1.5px] bg-white/40"></div>
                  </div>
                  <p>{unit.endsWith('h') ? 'h-' + unit : 'w-' + unit}</p>
                  <div className="grid h-full grid-rows-[1fr_1px] justify-items-center">
                    <div className="h-full w-[1.5px] bg-white/40"></div>
                    <div className="h-full w-[12px] bg-white/60"></div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
            </Stripes>
          </motion.div>
        </motion.div>
        {/* 地址栏动画 - 移动端和桌面端都支持 */}
        {unit.startsWith('lv') && (
          <motion.div
            transition={{
              ...transition,
            }}
            variants={{
              visible: {
                bottom: `-${viewport.navBarHeight}px`,
                height: `${viewport.navBarHeight}px`,
              },
              hidden: {
                bottom: "5px",
                height: "0px",
              },
            }}
            initial="visible"
            animate={hidden ? "hidden" : "visible"}
            // For some reason putting this in a tailwind class doesn't work
            style={{ width: `${viewport.width - 16}px` }}
            className={clsx(colorStyles, "absolute right-0 left-0 mx-auto rounded-b-md opacity-20")}
          ></motion.div>
        )}{" "}
      </div>
      <div
        style={{ backgroundPosition: "10px 10px" }}
        className="bg-grid-slate-100 dark:bg-grid-slate-700/25 pointer-events-none absolute inset-0 z-[-1] [mask-image:linear-gradient(0deg,#fff,rgba(255,255,255,0.6))] dark:[mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]"
      />
      <div className="pointer-events-none absolute inset-0 z-[-1] rounded-xl border border-black/5 dark:border-white/5" />
    </Example>
  );
}
