import { useState, useEffect } from 'react';

/**
 * 移动端检测 Hook
 * 支持多种检测方式：触摸事件、用户代理、屏幕尺寸
 */
export function useMobileDetection() {
  const [isMobile, setIsMobile] = useState(false);
  const [hasTouch, setHasTouch] = useState(false);
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [isMobileUserAgent, setIsMobileUserAgent] = useState(false);

  useEffect(() => {
    const checkMobileFeatures = () => {
      // 检测触摸支持
      const touchSupport =
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0

      // 检测屏幕尺寸
      const smallScreen = window.innerWidth < 768;

      // 检测用户代理
      const mobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS/i.test(
        navigator.userAgent
      );

      // 更新状态
      setHasTouch(touchSupport);
      setIsSmallScreen(smallScreen);
      setIsMobileUserAgent(mobileUserAgent);

      // 综合判断：满足以下任一条件即为移动端
      // 1. 小屏幕 + 触摸支持
      // 2. 移动端用户代理
      const mobile = (smallScreen && touchSupport) || mobileUserAgent;
      setIsMobile(mobile);
    };

    // 初始检测
    checkMobileFeatures();

    // 监听窗口大小变化
    const handleResize = () => {
      checkMobileFeatures();
    };

    window.addEventListener('resize', handleResize);

    // 监听方向变化（移动端特有）
    const handleOrientationChange = () => {
      // 延迟检测，等待方向变化完成
      setTimeout(checkMobileFeatures, 100);
    };

    window.addEventListener('orientationchange', handleOrientationChange);

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);

  return {
    /** 是否为移动端设备 */
    isMobile,
    /** 是否支持触摸事件 */
    hasTouch,
    /** 是否为小屏幕 */
    isSmallScreen,
    /** 是否为移动端用户代理 */
    isMobileUserAgent,
    /** 检测详情对象 */
    detection: {
      touchSupport: hasTouch,
      screenSize: isSmallScreen,
      userAgent: isMobileUserAgent,
      finalResult: isMobile
    }
  };
}

/**
 * 简化版移动端检测 Hook
 * 只返回布尔值结果
 */
export function useIsMobile(): boolean {
  const { isMobile } = useMobileDetection();
  return isMobile;
}

/**
 * 触摸事件检测 Hook
 * 专门用于检测设备是否支持触摸
 */
export function useTouchDetection() {
  const [hasTouch, setHasTouch] = useState(false);
  const [touchPoints, setTouchPoints] = useState(0);

  useEffect(() => {
    const checkTouchSupport = () => {
      // 检测各种触摸 API
      const hasTouchStart = 'ontouchstart' in window;
      const hasMaxTouchPoints = navigator.maxTouchPoints > 0;

      const touchSupported = hasTouchStart || hasMaxTouchPoints;
      const maxPoints = navigator.maxTouchPoints ||

        (hasTouchStart ? 10 : 0); // 假设支持 10 点触摸

      setHasTouch(touchSupported);
      setTouchPoints(maxPoints);
    };

    checkTouchSupport();
  }, []);

  return {
    /** 是否支持触摸 */
    hasTouch,
    /** 最大触摸点数 */
    touchPoints,
    /** 是否支持多点触摸 */
    isMultiTouch: touchPoints > 1
  };
}
